/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6f8ff',
          100: '#b3ecff',
          200: '#80dfff',
          300: '#4dd2ff',
          400: '#1ac5ff',
          500: '#00aeff', // primary blue
          600: '#0087cc',
          700: '#006699',
          800: '#004466',
          900: '#002233',
          950: '#001122',
        },
        alert: {
          50: '#fff0f3',
          100: '#ffe0e6',
          200: '#ffc2cd',
          300: '#ff99aa',
          400: '#ff667a',
          500: '#ff3d5a', // alert red
          600: '#e60026',
          700: '#bf0020',
          800: '#99001a',
          900: '#730014',
          950: '#4d000d',
        },
        dark: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
      },
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
        sans: ['Inter', 'sans-serif'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'ping-slow': 'ping 3s cubic-bezier(0, 0, 0.2, 1) infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'radar': 'radar 3s linear infinite',
      },
      keyframes: {
        glow: {
          '0%': { 
            boxShadow: '0 0 5px rgba(0, 174, 255, 0.5)' 
          },
          '100%': { 
            boxShadow: '0 0 20px rgba(0, 174, 255, 0.8)' 
          },
        },
        radar: {
          '0%': { transform: 'translate(-50%, -50%) scale(0.8)', opacity: '1' },
          '100%': { transform: 'translate(-50%, -50%) scale(1.5)', opacity: '0' },
        },
      },
    },
  },
  plugins: [],
};