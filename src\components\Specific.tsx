import React from 'react';

const Specific: React.FC = () => {
  return (
    <section id="specific" className="relative py-16 md:py-24">
      <div className="absolute inset-0 bg-gradient-to-b from-[#020617] to-[#0f172a] backdrop-blur-sm -z-10"></div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="grid gap-12 lg:grid-cols-2 items-center">
          
          {/* Image Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-4">
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                <img
                  src="https://www.commonground.work/wp-content/uploads/2022/05/TaylorsLakeside-scaled-e1653011961805.jpg"
                  alt="School"
                  className="w-full h-auto rounded-xl block"
                />
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                <img
                  src="https://media.timeout.com/images/106198915/image.jpg"
                  alt="Shopping Mall"
                  className="w-full h-auto rounded-xl block"
                />
              </div>
            </div>

            <div className="mt-12">
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                <img
                  src="https://steptodown.com/istock-downloader/images/steptodown.com614404.jpg"
                  alt="Working"
                  className="w-full h-auto rounded-xl block"
                />
              </div>
            </div>
          </div>

          {/* Text Content */}
          <div className="flex flex-col text-left pl-8">
            <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-4">
              <span className="block text-center">No App.</span>
              <span className="block mt-2 text-transparent bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-center">
                Just Your Mic.
              </span>
            </h1>
            <p className="text-lg text-gray-300 mt-8 mb-10">
              Turn on your microphone to receive gunshot alerts, threat levels, and safe exit routes — instantly and anywhere.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Specific;