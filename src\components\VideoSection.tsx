import React from 'react';
import { Play, Shield, Zap, Users } from 'lucide-react';

const VideoSection: React.FC = () => {
  return (
    <section
      id="video"
      className="relative py-8 bg-gradient-to-br from-[#0f172a] to-[#1e293b] overflow-hidden"
    >
      <div className="absolute inset-0 pointer-events-none bg-[radial-gradient(circle_at_25%_25%,rgba(59,130,246,0.1)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(139,92,246,0.1)_0%,transparent_50%)]"></div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
            See Our Technology in{' '}
            <span className="text-blue-400">Action</span>
          </h2>
          <p className="text-lg md:text-xl text-slate-400 max-w-2xl mx-auto leading-relaxed">
            Watch how our AI-powered gunshot detection system works in real-time scenarios
          </p>
        </div>

        <div className="flex justify-center mb-16">
          <div className="relative w-full max-w-4xl aspect-video rounded-xl overflow-hidden shadow-[0_25px_50px_-12px_rgba(0,0,0,0.5)] bg-slate-800 group">
            <iframe
              src="https://www.youtube.com/embed/dQw4w9WgXcQ"
              title="Gunshot Detection System Demo"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="w-full h-full border-none"
            ></iframe>

            <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center pointer-events-none">
              <button className="bg-blue-500 hover:bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center text-white transition-transform transform hover:scale-100 scale-90 pointer-events-auto">
                <Play size={32} />
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {/* Feature 1 */}
          <div className="text-center p-8 rounded-xl bg-slate-800/50 backdrop-blur-lg border border-blue-500/10 hover:translate-y-[-4px] hover:shadow-[0_20px_40px_-12px_rgba(59,130,246,0.2)] hover:border-blue-500/30 transition-all">
            <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center rounded-full bg-gradient-to-br from-blue-400 to-purple-400 text-white">
              <Shield size={24} />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Advanced AI Detection</h3>
            <p className="text-sm text-slate-400 leading-relaxed">
              Machine learning algorithms trained on thousands of audio samples
            </p>
          </div>

          {/* Feature 2 */}
          <div className="text-center p-8 rounded-xl bg-slate-800/50 backdrop-blur-lg border border-blue-500/10 hover:translate-y-[-4px] hover:shadow-[0_20px_40px_-12px_rgba(59,130,246,0.2)] hover:border-blue-500/30 transition-all">
            <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center rounded-full bg-gradient-to-br from-blue-400 to-purple-400 text-white">
              <Zap size={24} />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Lightning Fast Response</h3>
            <p className="text-sm text-slate-400 leading-relaxed">
              Sub-second detection and immediate alert distribution
            </p>
          </div>

          {/* Feature 3 */}
          <div className="text-center p-8 rounded-xl bg-slate-800/50 backdrop-blur-lg border border-blue-500/10 hover:translate-y-[-4px] hover:shadow-[0_20px_40px_-12px_rgba(59,130,246,0.2)] hover:border-blue-500/30 transition-all">
            <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center rounded-full bg-gradient-to-br from-blue-400 to-purple-400 text-white">
              <Users size={24} />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Community Protection</h3>
            <p className="text-sm text-slate-400 leading-relaxed">
              Protecting schools, offices, and public spaces nationwide
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default VideoSection;
