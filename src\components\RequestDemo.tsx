import React, { useState } from 'react';
import Button from './Button';
import { Send } from 'lucide-react';

const RequestDemo: React.FC = () => {
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    organization: '',
    requestType: 'demo'
  });
  
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formState);
    setIsSubmitted(true);
  };

  return (
    <section id="request-demo" className="py-16 md:py-24 relative">
      {/* Background with lines */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-dark-950 opacity-90"></div>
        <div className="absolute inset-0 opacity-10" 
          style={{ 
            backgroundImage: 'linear-gradient(0deg, #1e88e5 1px, transparent 1px), linear-gradient(90deg, #1e88e5 1px, transparent 1px)',
            backgroundSize: '40px 40px',
            backgroundPosition: '-1px -1px'
          }}
        ></div>
      </div>
      
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-5 gap-8 items-center">
            <div className="md:col-span-2">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to <span className="text-primary-400">Upgrade</span> Your Security?</h2>
              <p className="text-gray-300 mb-6">
                Request a personalized demo to see how RapidResponse can transform your security operations and emergency response capabilities.
              </p>
              <div className="bg-dark-800/50 backdrop-blur-sm rounded-lg p-4 border border-dark-700">
                <h3 className="text-lg font-semibold mb-3">What you'll get:</h3>
                <ul className="space-y-3">
                  {[
                    'Personalized system demonstration',
                    'Integration assessment for your infrastructure',
                    'Customized pricing proposal',
                    'Free trial period option'
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <span className="h-5 w-5 rounded-full bg-primary-500 flex items-center justify-center text-xs mr-2 flex-shrink-0">
                        {index + 1}
                      </span>
                      <span className="text-gray-300 text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="md:col-span-3 bg-dark-800/70 backdrop-blur-sm rounded-xl p-6 md:p-8 border border-dark-700">
              {!isSubmitted ? (
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formState.name}
                      onChange={handleChange}
                      className="w-full px-4 py-2 bg-dark-900 border border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 text-white"
                      required
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formState.email}
                      onChange={handleChange}
                      className="w-full px-4 py-2 bg-dark-900 border border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 text-white"
                      required
                    />
                  </div>
                  
                  <div className="mb-4">
                    <label htmlFor="organization" className="block text-sm font-medium text-gray-300 mb-1">
                      Organization
                    </label>
                    <input
                      type="text"
                      id="organization"
                      name="organization"
                      value={formState.organization}
                      onChange={handleChange}
                      className="w-full px-4 py-2 bg-dark-900 border border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 text-white"
                      required
                    />
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="requestType" className="block text-sm font-medium text-gray-300 mb-1">
                      Request Type
                    </label>
                    <select
                      id="requestType"
                      name="requestType"
                      value={formState.requestType}
                      onChange={handleChange}
                      className="w-full px-4 py-2 bg-dark-900 border border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 text-white"
                    >
                      <option value="demo">Product Demo</option>
                      <option value="pricing">Pricing Information</option>
                      <option value="technical">Technical Specifications</option>
                      <option value="consultation">Security Consultation</option>
                    </select>
                  </div>
                  
                  <Button type="submit" className="w-full flex items-center justify-center">
                    <Send size={18} className="mr-2" />
                    Request Your Demo
                  </Button>
                </form>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 mx-auto bg-primary-500/20 rounded-full flex items-center justify-center mb-4">
                    <Send size={32} className="text-primary-400" />
                  </div>
                  <h3 className="text-2xl font-bold mb-2">Request Submitted!</h3>
                  <p className="text-gray-300 mb-6">
                    Thank you for your interest in RapidResponse. Our team will contact you within 24 hours to schedule your personalized demo.
                  </p>
                  <Button 
                    variant="outline" 
                    className="mx-auto"
                    onClick={() => setIsSubmitted(false)}
                  >
                    Submit Another Request
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RequestDemo;