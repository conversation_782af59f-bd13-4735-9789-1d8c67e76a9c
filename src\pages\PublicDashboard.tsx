import React from 'react';
import { useDetection } from '../context/DetectionContext';
import LiveMap from '../components/LiveMap';
import { Shield, Eye } from 'lucide-react';

const PublicDashboard: React.FC = () => {
  const { safetyStatus } = useDetection();

  return (
    <div className={`min-h-screen transition-all duration-1000 ${
      safetyStatus === 'alert' 
        ? 'bg-gradient-to-br from-alert-950 via-dark-950 to-alert-900' 
        : 'bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900'
    }`}>
      
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5 z-0">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, ${
            safetyStatus === 'alert' ? '#ff3d5a' : '#00aeff'
          } 0%, transparent 50%), radial-gradient(circle at 75% 75%, ${
            safetyStatus === 'alert' ? '#ff3d5a' : '#00aeff'
          } 0%, transparent 50%)`,
          backgroundSize: '100px 100px'
        }}></div>
      </div>

      <div className="relative z-10 pt-8 pb-12 px-4 md:px-6">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-2">
              Safety <span className={safetyStatus === 'alert' ? 'text-alert-400' : 'text-green-400'}>Monitor</span>
            </h1>
            <p className="text-gray-400">Real-time public safety information</p>
          </div>

          {/* Safety Status */}
          <div className={`bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border transition-all duration-500 mb-6 ${
            safetyStatus === 'alert'
              ? 'border-alert-500/30 shadow-[0_0_30px_rgba(255,61,90,0.15)]'
              : 'border-green-500/30 shadow-[0_0_30px_rgba(34,197,94,0.15)]'
          }`}>
            <div className="text-center">
              <div className={`w-24 h-24 mx-auto rounded-full border-4 transition-all duration-300 flex items-center justify-center mb-4 ${
                safetyStatus === 'alert'
                  ? 'border-alert-500 bg-alert-500/10'
                  : 'border-green-500 bg-green-500/10'
              }`}>
                <Eye className={`w-12 h-12 ${
                  safetyStatus === 'alert' ? 'text-alert-400' : 'text-green-400'
                }`} />
              </div>

              <h3 className="text-xl font-bold mb-2">Safety Status</h3>

              <div className={`text-3xl font-bold mb-2 ${
                safetyStatus === 'alert' ? 'text-alert-400' : 'text-green-400'
              }`}>
                {safetyStatus === 'alert' ? 'ALERT ACTIVE' : 'ALL CLEAR'}
              </div>

              <p className="text-gray-400">
                {safetyStatus === 'alert' 
                  ? 'Security incident detected in your area'
                  : 'No security threats detected'
                }
              </p>
            </div>
          </div>

          {/* Bottom Grid: Live Map + Emergency Info */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* Live Location Tracking */}
            <LiveMap />

            {/* Emergency Info */}
            <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
              <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                <Shield className="text-primary-400" size={20} />
                Emergency Information
              </h3>

              <div className="space-y-4">
                <div className="p-4 bg-dark-800/50 rounded-lg">
                  <h4 className="font-semibold text-green-400 mb-2">Emergency Contacts</h4>
                  <div className="text-sm text-gray-300 space-y-1">
                    <div>Police: 999</div>
                    <div>Fire & Rescue: 994</div>
                    <div>Ambulance: 999</div>
                    <div>Royal Malaysia Police Control Center:</div>
                    <div>+60 (3) 2266 3333 / +60 (3) 2031 9999</div>
                  </div>
                </div>

                <div className="p-4 bg-dark-800/50 rounded-lg">
                  <h4 className="font-semibold text-primary-400 mb-2">Safety Tips</h4>
                  <div className="text-sm text-gray-300 space-y-1">
                    <div>• Stay calm and alert</div>
                    <div>• Follow official instructions</div>
                    <div>• Report suspicious activities</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default PublicDashboard;
