import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, AlertTriangleIcon } from 'lucide-react';

const steps = [
  {
    icon: <Target className="w-6 h-6 text-white" />,
    title: "Detection",
    description: "Our advanced sensors detect potential gunshot events with high precision and minimal false positives."
  },
  {
    icon: <Brain className="w-6 h-6 text-purple-400" />,
    title: "Analysis",
    description: "AI algorithms instantly analyze acoustic signatures to confirm gunshot events and determine their characteristics."
  },
  {
    icon: <AlertTriangleIcon className="w-6 h-6 text-red-400" />,
    title: "Alert",
    description: "Real-time alerts are generated and dispatched to relevant security personnel and emergency services."
  },
  {
    icon: <Shield className="w-6 h-6 text-green-400" />,
    title: "Response",
    description: "Coordinated response protocols are activated, enabling swift and effective emergency response."
  }
];

const HowItWorks: React.FC = () => {
  return (
    <section id="how-it-works" className="py-16 md:py-24 relative overflow-hidden">
      
      {/* Radar animation background with Tai<PERSON>wind only */}
        <div className="absolute top-1/2 left-1/2 w-1/2 h-1/2 max-w-[70rem] -translate-x-1/2 -translate-y-1/2 pointer-events-none">
        {/* Wave 1 */}
        <div className="absolute top-1/2 left-1/2 w-full h-full rounded-[100%_100%_100%_100%] border-2 border-blue-500/20 animate-[radar_3s_linear_infinite] -translate-x-1/2 -translate-y-1/2 delay-[0s]" />
        {/* Wave 2 */}
        <div className="absolute top-1/2 left-1/2 w-3/4 h-3/4 rounded-[100%_100%_100%_100%] border-2 border-blue-500/30 animate-[radar_3s_linear_infinite] -translate-x-1/2 -translate-y-1/2 delay-[1s]" />
        {/* Wave 3 */}
        <div className="absolute top-1/2 left-1/2 w-1/2 h-1/2 rounded-[100%_100%_100%_100%] border-2 border-blue-500/40 animate-[radar_3s_linear_infinite] -translate-x-1/2 -translate-y-1/2 delay-[2s]" />
      </div>

      <div className="absolute inset-0 bg-dark-900/50 backdrop-blur-sm -z-10"></div>

      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">How It <span className="text-primary-400">Works</span></h2>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Our advanced system uses cutting-edge AI technology to detect, classify, and respond to gunshot incidents with unprecedented speed and accuracy.
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          {/* Desktop version - horizontal timeline */}
          <div className="hidden md:block relative mt-20">
            {/* Connection line */}
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-dark-700 -translate-y-1/2"></div>

            <div className="flex justify-between relative">
              {steps.map((step, index) => (
                <div key={index} className="relative flex flex-col items-center w-1/5">
                  {/* Icon circle */}
                  <div className="z-10 w-16 h-16 rounded-full bg-dark-800 border-2 border-primary-500 flex items-center justify-center text-primary-400 mb-6">
                    <div className="w-8 h-8 flex items-center justify-center">
                      {step.icon}
                    </div>
                  </div>

                  {/* Text content */}
                  {/* Removed conditional margin and applied consistent mt-4 */}
                  <div className="text-center mt-4">
                    <h3 className="text-lg font-semibold mb-3">{step.title}</h3>
                    <p className="text-sm text-gray-400 max-w-[200px] mx-auto">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Mobile version - vertical steps */}
          <div className="md:hidden">
            <div className="relative">
              {/* Connection line */}
              <div className="absolute top-0 bottom-0 left-7 w-1 bg-dark-700"></div>

              {steps.map((step, index) => (
                <div key={index} className="relative flex mb-8">
                  {/* Icon circle */}
                  <div className="z-10 w-14 h-14 rounded-full bg-dark-800 border-2 border-primary-500 flex items-center justify-center text-primary-400 mr-6 flex-shrink-0">
                    <div className="w-6 h-6 flex items-center justify-center">
                      {step.icon}
                    </div>
                  </div>

                  {/* Text content */}
                  <div className="pt-2">
                    <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                    <p className="text-sm text-gray-400">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;