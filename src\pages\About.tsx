import React from 'react';
import { Brain, Database, Zap, Globe, Shield, Target, Clock, ArrowLeftRight } from 'lucide-react';

const About: React.FC = () => {
  const features = [
    {
      icon: <Brain className="w-8 h-8 text-red-400" />,
      title: "CNN Audio Classification",
      description: "5-layer Convolutional Neural Network trained on real-world gunshot and urban sound datasets for superior accuracy."
    },
    {
      icon: <Database className="w-8 h-8 text-purple-400" />,
      title: "Firebase Real-time Backend",
      description: "Cloud-powered real-time detection with automatic TTL cleanup and CSV export capabilities."
    },
    {
      icon: <Zap className="w-8 h-8 text-yellow-400" />,
      title: "Instant Alert System",
      description: "Sub-second response times with direct integration to MERS 999 emergency services in Malaysia."
    },
    {
      icon: <Globe className="w-8 h-8 text-green-400" />,
      title: "Single-Source GPS Tracking",
      description: "Precise geolocation tracking for rapid emergency response coordination."
    }
  ];

  const stats = [
    { icon: <Target className="text-white" />, value: "96.8%", label: "Detection Accuracy" },
    { icon: <Clock className="text-yellow-400" />, value: "<1s", label: "Response Time" },
    { icon: <Shield className="text-purple-400" />, value: "24/7", label: "Monitoring" },
    { icon: <ArrowLeftRight className="text-blue-400" />, value: "500m", label: "Detection Range" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900 pt-24 pb-12">
      <div className="container mx-auto px-4 md:px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Engineered for <span className="text-primary-400">Instant Action</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            RapidResponse represents cutting-edge AI technology designed for real-world emergency response scenarios.
            Built as a Final Year Project by Team 09.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30 text-center">
              <div className="flex justify-center mb-3">
                <div className="p-3 bg-primary-500/20 rounded-lg">
                  {React.cloneElement(stat.icon)}
                </div>
              </div>
              <div className="text-3xl font-bold text-green-400 mb-1">{stat.value}</div>
              <div className="text-sm text-gray-400">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* System Architecture */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">System <span className="text-primary-400">Architecture</span></h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30 hover:border-primary-400/50 transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-primary-500/20 rounded-lg flex-shrink-0">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                    <p className="text-gray-300">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Technical Specifications */}
        <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-8 border border-primary-500/30 mb-16">
          <h2 className="text-2xl font-bold mb-6 text-center">Technical <span className="text-primary-400">Specifications</span></h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4 text-primary-400">AI Model Details</h3>
              <ul className="space-y-2 text-gray-300">
                <li>• <strong>Architecture:</strong> 5-layer Convolutional Neural Network</li>
                <li>• <strong>Training Data:</strong> UrbanSound8K + Custom Gunshot Dataset</li>
                <li>• <strong>Input:</strong> 4-second audio segments, 44.1kHz sampling</li>
                <li>• <strong>Accuracy:</strong> 96.8% on validation set</li>
                <li>• <strong>False Positive Rate:</strong> &lt;3.2%</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4 text-primary-400">System Integration</h3>
              <ul className="space-y-2 text-gray-300">
                <li>• <strong>Backend:</strong> Firebase Realtime Database</li>
                <li>• <strong>Emergency Services:</strong> MERS 999 API Integration</li>
                <li>• <strong>Data Retention:</strong> Automatic TTL cleanup</li>
                <li>• <strong>Export:</strong> CSV format for analysis</li>
                <li>• <strong>Deployment:</strong> Cloud-native architecture</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Team Information */}
        <div className="text-center bg-dark-900/80 backdrop-blur-sm rounded-xl p-8 border border-primary-500/30">
          <h2 className="text-2xl font-bold mb-4">About the <span className="text-primary-400">Project</span></h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            RapidResponse was developed as a Final Year Project (FYP) by Team 09, focusing on creating 
            a practical AI solution for public safety and emergency response scenarios.
          </p>
          
          <div className="flex justify-center items-center gap-2 text-primary-400 font-medium">
            <Shield size={20} />
            <span>Built with ❤️ by FYP Team 09 · AI-Enabled Safety for All</span>
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default About;