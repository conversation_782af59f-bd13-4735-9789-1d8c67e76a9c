import React from 'react';
import { MapPin, Navigation } from 'lucide-react';
import { useDetection } from '../context/DetectionContext';

const LiveMap: React.FC = () => {
  const { alerts, safetyStatus } = useDetection();
  const recentAlerts = alerts.slice(0, 5);

  return (
    <div
      className={`bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border transition-all duration-500 ${
        safetyStatus === 'alert'
          ? 'border-alert-500/30'
          : 'border-primary-500/30'
      }`}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold flex items-center gap-2">
          <Navigation
            className={
              safetyStatus === 'alert' ? 'text-alert-400' : 'text-primary-400'
            }
            size={20}
          />
          Live Location Tracking
        </h3>
        <div className="text-xs text-gray-400">Kuala Lumpur, Malaysia</div>
      </div>

      {/* Simulated Map */}
      <div className="relative h-64 bg-dark-800/50 rounded-lg overflow-hidden mb-4">
        {/* Grid Background */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage:
              'linear-gradient(rgba(0,174,255,0.3) 1px, transparent 1px), linear-gradient(to right, rgba(0,174,255,0.3) 1px, transparent 1px)',
            backgroundSize: '20px 20px',
          }}
        ></div>

        {/* Location Markers */}
        {recentAlerts.map((alert) => (
          <div
            key={alert.id}
            className={`absolute w-4 h-4 rounded-full transition-all duration-500 ${
              alert.classification === 'gunshot' && !alert.clearedBy
                ? 'bg-alert-500 animate-pulse shadow-[0_0_20px_rgba(255,61,90,0.6)]'
                : alert.classification === 'gunshot'
                ? 'bg-alert-700'
                : 'bg-primary-500'
            }`}
            style={{
              top: `${50 + (alert.location.lat - 3.1390) * 2000}%`,
              left: `${50 + (alert.location.lng - 101.6869) * 2000}%`,
              zIndex:
                alert.classification === 'gunshot' && !alert.clearedBy ? 10 : 5,
            }}
            title={`${alert.classification} alert`}
          >
            {alert.classification === 'gunshot' && !alert.clearedBy && (
              <div className="absolute inset-0 rounded-full bg-alert-500 animate-ping"></div>
            )}
          </div>
        ))}

        {/* Center Device Location */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div
            className={`w-6 h-6 rounded-full border-2 ${
              safetyStatus === 'alert'
                ? 'border-alert-400 bg-alert-500/20'
                : 'border-primary-400 bg-primary-500/20'
            } flex items-center justify-center`}
          >
            <MapPin
              size={12}
              className={
                safetyStatus === 'alert'
                  ? 'text-alert-400'
                  : 'text-primary-400'
              }
            />
          </div>
          <div className="absolute top-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-400 whitespace-nowrap">
            Detection Device
          </div>
        </div>

        {/* Coordinates Display */}
        <div className="absolute bottom-2 left-2 text-xs text-gray-500 bg-dark-900/70 px-2 py-1 rounded">
          3.1390°N, 101.6869°E
        </div>
      </div>

      {/* Map Legend */}
      <div className="flex justify-between items-center text-xs">
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-alert-500"></div>
            <span className="text-gray-400">Active Gunshot</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-alert-700"></div>
            <span className="text-gray-400">Cleared Alert</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-primary-500"></div>
            <span className="text-gray-400">Non-Gunshot</span>
          </div>
        </div>
        <div className="text-gray-500">Range: 500m radius</div>
      </div>
    </div>
  );
};

export default LiveMap;
