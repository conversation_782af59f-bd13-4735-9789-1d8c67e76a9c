import React, { createContext, useContext, useState } from 'react';

export interface Alert {
  id: string;
  timestamp: Date;
  classification: 'gunshot' | 'non-gunshot';
  location: { lat: number; lng: number };
  clearedBy?: string;
  clearedAt?: Date;
  fireBursts?: number;
  remarks?: string;
  audioUrl?: string;
}

interface DetectionContextType {
  isDetecting: boolean;
  setIsDetecting: (status: boolean) => void;
  safetyStatus: 'safe' | 'alert';
  runDetection: () => Promise<void>;
  alerts: Alert[];
  clearAlert: (alertId: string, adminName: string) => void;
  clearAllAlerts: () => void;
}

const DetectionContext = createContext<DetectionContextType | undefined>(undefined);

export const useDetection = () => {
  const context = useContext(DetectionContext);
  if (!context) {
    throw new Error('useDetection must be used within a DetectionProvider');
  }
  return context;
};

export const DetectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isDetecting, setIsDetecting] = useState(false);
  const [safetyStatus, setSafetyStatus] = useState<'safe' | 'alert'>('safe');
  const [alerts, setAlerts] = useState<Alert[]>([]);

  const runDetection = async () => {
    try {
      setIsDetecting(true);

      const res = await fetch('http://localhost:5000/predict', {
        method: 'POST'
      });

      if (!res.ok) throw new Error('Detection failed');

      const data = await res.json();

      const isGunshot = data.label?.toLowerCase() === 'gun_shot';

      const newAlert: Alert = {
        id: Date.now().toString(),
        timestamp: new Date(data.timestamp),
        classification: isGunshot ? 'gunshot' : 'non-gunshot',
        location: { lat: 3.1390, lng: 101.6869 }, // placeholder GPS
        remarks: '',
        fireBursts: undefined,
        audioUrl: data.audio_url || ''
      };

      setAlerts(prev => [newAlert, ...prev]);
      setSafetyStatus(isGunshot ? 'alert' : 'safe');

      if (isGunshot && data.audio_url) {
        const audio = new Audio(`http://localhost:5000${data.audio_url}`);
        audio.play();
      }
    } catch (err) {
      console.error('❌ Detection error:', err);
    } finally {
      setIsDetecting(false);
    }
  };

  const clearAlert = (alertId: string, adminName: string) => {
    setAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId
          ? { ...alert, clearedBy: adminName, clearedAt: new Date() }
          : alert
      )
    );

    const activeGunshots = alerts.filter(a => a.classification === 'gunshot' && !a.clearedBy);
    if (activeGunshots.length <= 1) {
      setSafetyStatus('safe');
    }
  };

  const clearAllAlerts = () => {
    setAlerts([]);
    setSafetyStatus('safe');
  };

  return (
    <DetectionContext.Provider
      value={{
        isDetecting,
        setIsDetecting,
        safetyStatus,
        runDetection,
        alerts,
        clearAlert,
        clearAllAlerts
      }}
    >
      {children}
    </DetectionContext.Provider>
  );
};
