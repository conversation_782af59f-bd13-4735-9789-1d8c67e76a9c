import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ChevronRight, Eye, BarChart, MapPin, Clock, Lock, Globe } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const RoleSelection: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState<'public' | 'admin' | null>(null);
  const navigate = useNavigate();

  const handleRoleSelect = (role: 'public' | 'admin') => {
    setSelectedRole(role);
  };

  const handleContinue = () => {
    if (selectedRole === 'public') {
      navigate('/login/public');
    } else if (selectedRole === 'admin') {
      navigate('/login/admin');
    }
  };

  return (
    <section id='role-selection'>
    <div className="min-h-screen bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900 relative overflow-hidden">
      {/* Grid Background */}
      <div className="absolute inset-0 opacity-10 bg-[linear-gradient(#1e88e5_1px,transparent_1px),linear-gradient(to_right,#1e88e5_1px,transparent_1px)] bg-[length:40px_40px]"></div>

      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="max-w-4xl w-full">
          {/* Header */}
          <div className="text-center mb-12"> 
            <h1 className="font-bold text-4xl lg:text-5xl leading-snug mb-6">
              Welcome to RapidResponse.
            <br />
            <span className="text-blue-400">Choose Your Role.</span>
          </h1>
          </div>

          {/* Role Cards */}
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            {/* Public Access Card */}
            <div
              onClick={() => handleRoleSelect('public')}
              className={`relative p-8 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                selectedRole === 'public'
                  ? 'border-green-500/50 bg-green-500/10 shadow-[0_0_30px_rgba(34,197,94,0.3)]'
                  : 'border-dark-700 bg-dark-900/80 hover:border-green-500/30 hover:bg-green-500/5'
              } backdrop-blur-sm group`}
            >
              <div className="flex items-start gap-4 mb-6">
                <div className={`p-4 rounded-xl transition-all duration-300 ${
                  selectedRole === 'public' 
                    ? 'bg-green-500/20 border border-green-500/30' 
                    : 'bg-green-500/10 border border-green-500/20 group-hover:bg-green-500/15'
                }`}>
                  <Users className="w-8 h-8 text-green-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold mb-2 text-green-400">Public Access</h3>
                  <p className="text-gray-400">Access public safety information and real-time alerts</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3 text-gray-300">
                  <Eye size={16} className="text-green-400" />
                  <span>View public safety alerts</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Shield size={16} className="text-green-400" />
                  <span>Access emergency information</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <BarChart size={16} className="text-green-400" />
                  <span>Basic incident reports</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Clock size={16} className="text-green-400" />
                  <span>Real-time safety updates</span>
                </div>
              </div>

              {selectedRole === 'public' && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-white"></div>
                  </div>
                </div>
              )}
            </div>

            {/* Administrator Card */}
            <div
              onClick={() => handleRoleSelect('admin')}
              className={`relative p-8 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                selectedRole === 'admin'
                  ? 'border-purple-500/50 bg-purple-500/10 shadow-[0_0_30px_rgba(168,85,247,0.3)]'
                  : 'border-dark-700 bg-dark-900/80 hover:border-purple-500/30 hover:bg-purple-500/5'
              } backdrop-blur-sm group`}
            >
              <div className="flex items-start gap-4 mb-6">
                <div className={`p-4 rounded-xl transition-all duration-300 ${
                  selectedRole === 'admin' 
                    ? 'bg-purple-500/20 border border-purple-500/30' 
                    : 'bg-purple-500/10 border border-purple-500/20 group-hover:bg-purple-500/15'
                }`}>
                  <Settings className="w-8 h-8 text-purple-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold mb-2 text-purple-400">Administrator</h3>
                  <p className="text-gray-400">Full system access with management capabilities</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3 text-gray-300">
                  <Settings size={16} className="text-purple-400" />
                  <span>System configuration</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Users size={16} className="text-purple-400" />
                  <span>User management</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <BarChart size={16} className="text-purple-400" />
                  <span>Analytics & reporting</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <MapPin size={16} className="text-purple-400" />
                  <span>Device management</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Lock size={16} className="text-purple-400" />
                  <span>Security settings</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <Globe size={16} className="text-purple-400" />
                  <span>Audit logs</span>
                </div>
              </div>

              {selectedRole === 'admin' && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-white"></div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Continue Button */}
          <div className="text-center">
            <button
              onClick={handleContinue}
              disabled={!selectedRole}
              className={`px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center gap-3 mx-auto ${
                selectedRole
                  ? selectedRole === 'public'
                    ? 'bg-green-500 hover:bg-green-600 text-white shadow-[0_0_20px_rgba(34,197,94,0.3)] hover:shadow-[0_0_30px_rgba(34,197,94,0.5)]'
                    : 'bg-purple-500 hover:bg-purple-600 text-white shadow-[0_0_20px_rgba(168,85,247,0.3)] hover:shadow-[0_0_30px_rgba(168,85,247,0.5)]'
                  : 'bg-gray-700 text-gray-400 cursor-not-allowed'
              }`}
            >
              {selectedRole ? `Continue as ${selectedRole === 'public' ? 'Public User' : 'Administrator'}` : 'Select a Role to Continue'}
              {selectedRole && <ChevronRight size={20} />}
            </button>
          </div>
        </div>
      </div>
    </div>
    </section>
  );
};

export default RoleSelection;