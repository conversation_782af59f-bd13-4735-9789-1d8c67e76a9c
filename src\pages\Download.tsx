import React from 'react';
import { Download as DownloadIcon, Monitor, Smartphone, Settings, Shield, Zap, Database } from 'lucide-react';

const Download: React.FC = () => {
  const features = [
    {
      icon: <Zap className="w-5 h-5 text-primary-400" />,
      title: "Auto-record every 4 seconds",
      description: "Continuous monitoring with optimized buffer management"
    },
    {
      icon: <Database className="w-5 h-5 text-primary-400" />,
      title: "Real-time dashboard sync",
      description: "Instant detection results pushed to live monitoring system"
    },
    {
      icon: <Shield className="w-5 h-5 text-primary-400" />,
      title: "No local audio storage",
      description: "Privacy-focused design with secure cloud processing"
    },
    {
      icon: <Settings className="w-5 h-5 text-primary-400" />,
      title: "Easy configuration",
      description: "Simple setup with automatic device detection"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900 pt-24 pb-12">
      <div className="container mx-auto px-4 md:px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Download <span className="text-primary-400">Desktop Agent</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Install the RapidResponse desktop application for continuous monitoring and seamless integration with our cloud-based detection system.
          </p>
        </div>

        {/* Main Download Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Desktop App Preview */}
            <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-8 border border-primary-500/30">
              <div className="text-center mb-6">
                <div className="w-20 h-20 mx-auto bg-primary-500/20 rounded-xl flex items-center justify-center mb-4">
                  <Monitor className="w-10 h-10 text-primary-400" />
                </div>
                <h2 className="text-2xl font-bold mb-2">Desktop Application</h2>
                <p className="text-gray-400">Windows, macOS, and Linux compatible</p>
              </div>

              {/* App Screenshot Mockup */}
              <div className="bg-dark-800 rounded-lg p-4 mb-6">
                <div className="bg-dark-700 rounded-t p-2 flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-alert-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div className="flex-1 text-center text-xs text-gray-400">RapidResponse Agent</div>
                </div>
                <div className="bg-dark-900 p-4 rounded-b">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-sm text-green-400">Recording Active</span>
                    </div>
                    <span className="text-xs text-gray-400">v2.1.0</span>
                  </div>
                  <div className="space-y-2">
                    <div className="h-2 bg-primary-500/30 rounded"></div>
                    <div className="h-2 bg-primary-500/20 rounded w-3/4"></div>
                    <div className="h-2 bg-primary-500/10 rounded w-1/2"></div>
                  </div>
                  <div className="mt-3 text-xs text-gray-500 text-center">
                    Last sync: 2 seconds ago
                  </div>
                </div>
              </div>

              {/* Download Button */}
              <button className="w-full bg-primary-500 hover:bg-primary-600 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-[0_0_20px_rgba(0,174,255,0.3)] hover:shadow-[0_0_30px_rgba(0,174,255,0.5)]">
                <DownloadIcon size={24} />
                <span className="text-lg">Download Desktop Agent</span>
              </button>
              
              <p className="text-xs text-gray-400 text-center mt-3">
                Version 2.1.0 • 45.2 MB • Free Download
              </p>
            </div>

            {/* Features List */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold">Key <span className="text-primary-400">Features</span></h3>
              
              {features.map((feature, index) => (
                <div key={index} className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-primary-500/20 rounded-lg flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h4 className="font-bold mb-1">{feature.title}</h4>
                      <p className="text-gray-400 text-sm">{feature.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* System Requirements */}
        <div className="max-w-4xl mx-auto mb-16">
          <h3 className="text-2xl font-bold text-center mb-8">System <span className="text-primary-400">Requirements</span></h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
              <h4 className="font-bold mb-4 flex items-center gap-2">
                <Monitor size={20} className="text-primary-400" />
                Windows
              </h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li>• Windows 10 or later</li>
                <li>• 4GB RAM minimum</li>
                <li>• 100MB free disk space</li>
                <li>• Microphone access</li>
                <li>• Internet connection</li>
              </ul>
            </div>

            <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
              <h4 className="font-bold mb-4 flex items-center gap-2">
                <Monitor size={20} className="text-primary-400" />
                macOS
              </h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li>• macOS 10.15 or later</li>
                <li>• 4GB RAM minimum</li>
                <li>• 100MB free disk space</li>
                <li>• Microphone permissions</li>
                <li>• Internet connection</li>
              </ul>
            </div>

            <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
              <h4 className="font-bold mb-4 flex items-center gap-2">
                <Monitor size={20} className="text-primary-400" />
                Linux
              </h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li>• Ubuntu 18.04+ / CentOS 7+</li>
                <li>• 4GB RAM minimum</li>
                <li>• 100MB free disk space</li>
                <li>• ALSA/PulseAudio</li>
                <li>• Internet connection</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Installation Guide */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-8 border border-primary-500/30">
            <h3 className="text-2xl font-bold mb-6 text-center">Installation <span className="text-primary-400">Guide</span></h3>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-bold mb-4 text-primary-400">Quick Setup</h4>
                <ol className="space-y-3 text-gray-300">
                  <li className="flex items-start gap-3">
                    <span className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0">1</span>
                    <span>Download the installer for your operating system</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0">2</span>
                    <span>Run the installer and follow the setup wizard</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0">3</span>
                    <span>Grant microphone permissions when prompted</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0">4</span>
                    <span>Connect to your RapidResponse dashboard</span>
                  </li>
                </ol>
              </div>
              
              <div>
                <h4 className="font-bold mb-4 text-primary-400">Configuration</h4>
                <ul className="space-y-3 text-gray-300">
                  <li>• <strong>Audio Settings:</strong> Automatic device detection</li>
                  <li>• <strong>Sensitivity:</strong> Adjustable detection threshold</li>
                  <li>• <strong>Network:</strong> Secure HTTPS connection</li>
                  <li>• <strong>Privacy:</strong> No local audio file storage</li>
                  <li>• <strong>Updates:</strong> Automatic background updates</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Download;