import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, Trash2, Download } from 'lucide-react';
import { useDetection } from '../context/DetectionContext';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const AlertHistory: React.FC = () => {
  const { alerts, clearAllAlerts } = useDetection();
  const [adminName, setAdminName] = useState('Admin User');
  const [startDate, setStartDate] = useState<Date | null>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(new Date());

  const [remarksMap, setRemarksMap] = useState<Record<string, string>>({});
  const [fireBurstsMap, setFireBurstsMap] = useState<Record<string, number>>({});
  const [classificationMap, setClassificationMap] = useState<Record<string, string>>({});

  const formatFullDateTime = (date: Date) =>
    date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

  const filteredAlerts = useMemo(() => {
    if (!startDate || !endDate) return [];
    const start = new Date(startDate.setHours(0, 0, 0, 0));
    const end = new Date(endDate.setHours(23, 59, 59, 999));
    return alerts.filter(alert => alert.timestamp >= start && alert.timestamp <= end);
  }, [alerts, startDate, endDate]);

  const exportToCSV = () => {
    const headers = ['Timestamp', 'Classification', 'Location (Google Map)', 'Status', 'Number of Firebursts', 'Remarks'];
    const rows = filteredAlerts.map(alert => {
      const date = new Date(alert.timestamp);
      const timestamp = `${date.toLocaleDateString('en-US')} ${date.toLocaleTimeString('en-US', { hour12: false })}`;
      const classification = classificationMap[alert.id] || alert.classification;
      const googleMapLink = `https://maps.google.com/?q=${alert.location.lat},${alert.location.lng}`;
      const status = alert.classification === 'gunshot' ? 'Gunshot' : 'Non-Gunshot';
      const bursts = alert.classification === 'gunshot' ? fireBurstsMap[alert.id] ?? '' : '';
      const remarks = remarksMap[alert.id] ?? '';

      return [timestamp, classification, googleMapLink, status, bursts.toString(), remarks];
    });

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'RapidResponse_Alert_History.csv';
    link.click();
  };

  return (
    <div className="bg-dark-800 rounded-xl p-6 shadow-md">
      <div className="flex flex-wrap justify-between items-center mb-4 gap-4">
        <input
          type="text"
          value={adminName}
          onChange={e => setAdminName(e.target.value)}
          className="bg-dark-700 text-white px-4 py-2 rounded-md border border-dark-600 w-full sm:w-auto"
        />
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-gray-400">Date Range:</span>
          <DatePicker
            selected={startDate}
            onChange={date => setStartDate(date)}
            className="bg-dark-700 text-white px-3 py-1 rounded-md border border-dark-600 text-sm w-[130px]"
            dateFormat="MM/dd/yyyy"
          />
          <DatePicker
            selected={endDate}
            onChange={date => setEndDate(date)}
            className="bg-dark-700 text-white px-3 py-1 rounded-md border border-dark-600 text-sm w-[130px]"
            dateFormat="MM/dd/yyyy"
          />
          <span className="text-xs text-gray-500 ml-2">{filteredAlerts.length} alerts</span>
        </div>
      </div>

      <div className="flex justify-between mb-4 gap-4 flex-wrap">
        <button
          onClick={exportToCSV}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center gap-2"
        >
          <Download size={16} />
          Export CSV
        </button>
        <button
          onClick={clearAllAlerts}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded flex items-center gap-2"
        >
          <Trash2 size={16} />
          Clear All
        </button>
      </div>

      <div className="space-y-4">
        {filteredAlerts.map(alert => (
          <div
            key={alert.id}
            className={`p-4 rounded-lg shadow-md ${
              alert.classification === 'gunshot' ? 'bg-red-900/30' : 'bg-green-900/20'
            }`}
          >
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center gap-2 text-sm text-white">
                {alert.classification === 'gunshot' ? (
                  <AlertTriangle className="text-red-400" size={18} />
                ) : (
                  <CheckCircle className="text-green-400" size={18} />
                )}
                <input
                  type="text"
                  value={classificationMap[alert.id] ?? alert.classification}
                  onChange={e =>
                    setClassificationMap({ ...classificationMap, [alert.id]: e.target.value })
                  }
                  className="bg-transparent border-b border-gray-400 focus:outline-none focus:border-white text-white w-40"
                />
              </div>
              <div className="text-xs text-gray-400">{formatFullDateTime(alert.timestamp)}</div>
            </div>

            <div className="text-sm text-gray-300 mb-1">
              Location: {alert.location.lat.toFixed(4)}, {alert.location.lng.toFixed(4)}
            </div>

            {alert.classification === 'gunshot' && (
              <input
                type="number"
                placeholder="Number of bursts"
                value={fireBurstsMap[alert.id] ?? ''}
                onChange={e =>
                  setFireBurstsMap({ ...fireBurstsMap, [alert.id]: parseInt(e.target.value) })
                }
                className="bg-dark-700 text-white px-2 py-1 rounded-md w-full md:w-48 mb-2"
              />
            )}

            <textarea
              placeholder="Write a remark..."
              value={remarksMap[alert.id] ?? ''}
              onChange={e => setRemarksMap({ ...remarksMap, [alert.id]: e.target.value })}
              className="bg-dark-700 text-white px-2 py-1 rounded-md w-full"
              rows={2}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default AlertHistory;
