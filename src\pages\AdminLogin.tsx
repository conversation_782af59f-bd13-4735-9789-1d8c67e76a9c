import React, { useState } from 'react';
import { Shield, Lock, ArrowLeft, AlertTriangle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const AdminLogin: React.FC = () => {
  const [adminCode, setAdminCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Simulate authentication delay
    setTimeout(() => {
      if (adminCode === '123') {
        navigate('/dashboard/admin');
      } else {
        setError('Invalid admin code. Please try again.');
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(circle at 25% 25%, #00aeff 0%, transparent 50%), radial-gradient(circle at 75% 75%, #00aeff 0%, transparent 50%)',
          backgroundSize: '100px 100px'
        }}></div>
      </div>

      {/* Grid Background */}
      <div className="absolute inset-0 opacity-[0.03]" style={{
        backgroundImage: 'linear-gradient(#1e88e5 1px, transparent 1px), linear-gradient(to right, #1e88e5 1px, transparent 1px)',
        backgroundSize: '40px 40px',
      }}></div>

      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="max-w-md w-full">
          {/* Back Button */}
          <button
            onClick={() => navigate('/')}
            className="flex items-center gap-2 text-gray-400 hover:text-white mb-6 transition-colors"
          >
            <ArrowLeft size={20} />
            <span>Back to Role Selection</span>
          </button>

          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-3 rounded-xl bg-purple-500/20 border border-purple-500/30">
                <Shield className="h-8 w-8 text-purple-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold tracking-tight">
                  Rapid<span className="text-purple-400">Response</span>
                </h1>
                <div className="text-xs text-gray-400">Administrator Access</div>
              </div>
            </div>
            
            <h2 className="text-xl font-bold mb-2">Admin Login</h2>
            <p className="text-gray-400">Enter your admin code to continue</p>
          </div>

          {/* Login Form */}
          <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-8 border border-purple-500/30">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Admin Code
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="password"
                    value={adminCode}
                    onChange={(e) => setAdminCode(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 bg-dark-800 border border-dark-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white"
                    placeholder="Enter admin code"
                    required
                  />
                </div>
                {error && (
                  <div className="mt-2 flex items-center gap-2 text-alert-400 text-sm">
                    <AlertTriangle size={16} />
                    <span>{error}</span>
                  </div>
                )}
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 shadow-[0_0_20px_rgba(168,85,247,0.3)] hover:shadow-[0_0_30px_rgba(168,85,247,0.5)]"
              >
                {isLoading ? 'Verifying...' : 'Access Admin Dashboard'}
              </button>
            </form>

            {/* Security Notice */}
            <div className="mt-6 p-4 bg-dark-800/50 rounded-lg border border-purple-500/20">
              <div className="flex items-start gap-3">
                <Lock className="text-purple-400 flex-shrink-0 mt-0.5" size={16} />
                <div>
                  <h4 className="text-sm font-medium text-purple-400 mb-1">Security Notice</h4>
                  <p className="text-xs text-gray-400">
                    Admin access is restricted and monitored. All activities are logged for security purposes.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;