import React, { useState } from 'react';
import { Send, Mail, MessageSquare, Bug, Lightbulb, Settings, Shield } from 'lucide-react';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: 'feedback',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Contact form submitted:', formData);
    setIsSubmitted(true);
  };

  const subjectOptions = [
    { value: 'bug', label: 'Bug Report', icon: <Bug size={16} /> },
    { value: 'feedback', label: 'General Feedback', icon: <MessageSquare size={16} /> },
    { value: 'technical', label: 'Technical Support', icon: <Settings size={16} /> },
    { value: 'feature', label: 'Feature Request', icon: <Lightbulb size={16} /> }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900 pt-24 pb-12">
      <div className="container mx-auto px-4 md:px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Get in <span className="text-primary-400">Touch</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Have questions, feedback, or need technical support? We're here to help improve your RapidResponse experience.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-5 gap-8">
            {/* Contact Info */}
            <div className="md:col-span-2 space-y-6">
              <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
                <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                  <Shield className="text-primary-400" size={24} />
                  Project Information
                </h3>
                <div className="space-y-4 text-gray-300">
                  <div>
                    <h4 className="font-semibold text-primary-400 mb-1">Final Year Project</h4>
                    <p className="text-sm">AI-Powered Gunshot Detection System</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary-400 mb-1">Team</h4>
                    <p className="text-sm">FYP Team 09</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary-400 mb-1">Institution</h4>
                    <p className="text-sm">Taylor's University Campus Lakeside</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary-400 mb-1">Academic Year</h4>
                    <p className="text-sm">2024/2025</p>
                  </div>
                </div>
              </div>

              <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
                <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                  <Mail className="text-primary-400" size={24} />
                  Contact Methods
                </h3>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-center gap-3">
                    <Mail size={16} className="text-primary-400" />
                    <span className="text-sm"><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <MessageSquare size={16} className="text-primary-400" />
                    <span className="text-sm">GitHub Issues & Discussions</span>
                  </div>
                </div>
              </div>

              <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border border-primary-500/30">
                <h3 className="text-lg font-bold mb-3">Response Time</h3>
                <p className="text-sm text-gray-300">
                  We typically respond to inquiries within 24-48 hours during academic periods. 
                  For urgent technical issues, please mark your subject as "Technical Support".
                </p>
              </div>
            </div>

            {/* Contact Form */}
            <div className="md:col-span-3">
              <div className="bg-dark-900/80 backdrop-blur-sm rounded-xl p-8 border border-primary-500/30">
                {!isSubmitted ? (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2">
                        Subject Category
                      </label>
                      <select
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white"
                      >
                        {subjectOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                        Message
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleChange}
                        rows={6}
                        className="w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-white resize-none"
                        placeholder="Please provide as much detail as possible..."
                        required
                      />
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-primary-500 hover:bg-primary-600 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-3 shadow-[0_0_20px_rgba(0,174,255,0.3)] hover:shadow-[0_0_30px_rgba(0,174,255,0.5)]"
                    >
                      <Send size={20} />
                      Send Message
                    </button>
                  </form>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 mx-auto bg-primary-500/20 rounded-full flex items-center justify-center mb-6">
                      <Send size={32} className="text-primary-400" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4">Message Sent Successfully!</h3>
                    <p className="text-gray-300 mb-6">
                      Thank you for contacting us. We've received your message and will respond within 24-48 hours.
                    </p>
                    <button
                      onClick={() => {
                        setIsSubmitted(false);
                        setFormData({ name: '', email: '', subject: 'feedback', message: '' });
                      }}
                      className="px-6 py-3 bg-dark-700 hover:bg-dark-600 text-gray-300 rounded-lg transition-colors"
                    >
                      Send Another Message
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 pt-8 border-t border-dark-800">
          <div className="flex justify-center items-center gap-2 text-primary-400 font-medium">
            <Shield size={20} />
            <span>Built with ❤️ by FYP Team 09 · AI-Enabled Safety for All</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;