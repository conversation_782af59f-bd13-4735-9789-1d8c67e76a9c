import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  className?: string;
  onClick?: () => void;
  href?: string;
  type?: 'button' | 'submit' | 'reset';
}

const Button: React.FC<ButtonProps> = ({ 
  children, 
  variant = 'primary', 
  className = '',
  onClick,
  href,
  type = 'button'
}) => {
  const baseClasses = "px-6 py-3 rounded-md font-medium transition-all duration-300 inline-block text-center";
  
  const variantClasses = {
    primary: "bg-primary-500 hover:bg-primary-600 text-white animate-glow",
    secondary: "bg-alert-500 hover:bg-alert-600 text-white",
    outline: "bg-transparent border border-primary-500 text-primary-500 hover:bg-primary-500/10"
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${className}`;
  
  if (href) {
    return (
      <a href={href} className={classes} onClick={onClick}>
        {children}
      </a>
    );
  }
  
  return (
    <button type={type} className={classes} onClick={onClick}>
      {children}
    </button>
  );
};

export default Button;