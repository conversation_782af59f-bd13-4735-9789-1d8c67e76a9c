import { useLocation } from 'react-router-dom';
import { useDetection } from '../context/DetectionContext';
import { useNavigate } from 'react-router-dom';
import React, { useState, useEffect } from "react";
import { Menu, X , LogOut} from "lucide-react";
import Button from './Button';

const Navbar: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const location = useLocation();
  const isHomePage = location.pathname === '/';
  const isPublicPage = location.pathname === '/dashboard/public';
  const isAdminPage = location.pathname === '/dashboard/admin';
  const isAboutPage = location.pathname === '/about';
  const isContactPage = location.pathname === '/contact';

  const handleScroll = () => {
    setIsScrolled(window.scrollY > 10);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const { safetyStatus } = useDetection();
  const navigate = useNavigate();

  const handleLogout = () => {
    navigate('/');
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? "bg-black/90 backdrop-blur-md py-3 shadow-lg" : "bg-transparent py-5"}`}>
      <div className="max-w-7xl mx-auto px-4 flex justify-between items-center">
        <a href="#" className="flex items-center gap-2 text-white">
          <img src="/rapidresponse.svg" alt="Rapid Response Logo" className="h-8 w-8 text-blue-500" />
          <span className="text-lg font-bold tracking-tight">Rapid<span className="text-blue-400">Response</span></span>
        </a>

        {isHomePage && (
          <div className="hidden md:flex items-center gap-8">
            <a href="#how-it-works" className="text-sm text-gray-300 hover:text-white">How It Works</a>
            <a href="#why-choose" className="text-sm text-gray-300 hover:text-white">Why Choose Us</a>
            <a href="#faqs" className="text-sm text-gray-300 hover:text-white">FAQs</a>
            <a href="/about" className="text-sm text-gray-300 hover:text-white">About Us</a>
            <a href="/contact" className="text-sm text-gray-300 hover:text-white">Contact Us</a>
            <Button href="#role-selection" variant="primary">Sign Up</Button>
          </div>
        )}

        {(isAboutPage || isContactPage) && (
          <div className="hidden md:flex items-center gap-8">
            <a href="/about" className="text-sm text-gray-300 hover:text-white">About Us</a>
            <a href="/contact" className="text-sm text-gray-300 hover:text-white">Contact Us</a>
            <Button href="/" variant="primary">Home</Button>
          </div>
        )}

        {(isPublicPage || isAdminPage) && (
          <div className="hidden md:flex items-center gap-4">
            <button 
              onClick={handleLogout}
              className="px-4 py-2 rounded-md bg-blue-500 text-white text-sm font-medium hover:bg-blue-600 w-fit flex items-center gap-2">
                <LogOut size={16} />
                <span className="text-sm">{isPublicPage ? "Sign Out" : "Log Out"}</span>
            </button>
          </div>
        )}

        <button className="md:hidden text-gray-300 hover:text-white" onClick={toggleMenu}>
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-black/95 backdrop-blur-md shadow-lg px-4 py-4 flex flex-col gap-4">

          {isHomePage && (
            <>
              <a href="#how-it-works" className="text-sm text-gray-300 hover:text-white" onClick={() => setIsMenuOpen(false)}>How It Works</a>
              <a href="#why-choose" className="text-sm text-gray-300 hover:text-white" onClick={() => setIsMenuOpen(false)}>Why Choose Us</a>
              <a href="#faqs" className="text-sm text-gray-300 hover:text-white">FAQs</a>
              <a href="/about" className="text-sm text-gray-300 hover:text-white">About Us</a>
              <a href="/contact" className="text-sm text-gray-300 hover:text-white">Contact Us</a>
              <a href="#role-selection" className="px-4 py-2 rounded-md bg-blue-500 text-white text-sm font-medium w-fit hover:bg-blue-600" onClick={() => setIsMenuOpen(false)}>Sign Up</a>
            </>
          )}

          {(isPublicPage || isAdminPage) && (
            <button 
              onClick={() => {
                setIsMenuOpen(false);
                handleLogout();
              }}
              className="px-4 py-2 rounded-md bg-blue-500 text-white text-sm font-medium hover:bg-blue-600 w-fit flex items-center gap-2"
            >
              <LogOut size={16} />
              <span className="text-sm">{isPublicPage ? "Sign Out" : "Log Out"}</span>
            </button>
          )}

          {(isAboutPage || isContactPage) && (
            <>
              <a href="/about" className="text-sm text-gray-300 hover:text-white">About Us</a>
              <a href="/contact" className="text-sm text-gray-300 hover:text-white">Contact Us</a>
              <a href="/" className="px-4 py-2 rounded-md bg-blue-500 text-white text-sm font-medium w-fit hover:bg-blue-600" onClick={() => setIsMenuOpen(false)}>Home</a>
            </>
          )}

        </div>
      )}

    </nav>
  );
};

export default Navbar;