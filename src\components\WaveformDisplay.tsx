import React from 'react';
import { useDetection } from '../context/DetectionContext';

const WaveformDisplay: React.FC = () => {
  const { isDetecting, safetyStatus } = useDetection();

  return (
    <div className={`bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border transition-all duration-500 ${
      safetyStatus === 'alert' 
        ? 'border-alert-500/30' 
        : 'border-primary-500/30'
    }`}>
      <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
        <div className={`w-2 h-2 rounded-full ${
          isDetecting 
            ? safetyStatus === 'alert' ? 'bg-alert-400 animate-pulse' : 'bg-primary-400 animate-pulse'
            : 'bg-gray-500'
        }`}></div>
        Audio Processing
      </h3>

      {/* Waveform Animation */}
      <div className="relative h-32 bg-dark-800/50 rounded-lg overflow-hidden mb-4">
        {isDetecting ? (
          <div className="absolute inset-0 flex items-center justify-center">
            {/* Pulsing Radar Rings */}
            <div className="relative w-24 h-24">
              <div className={`absolute inset-0 rounded-full border-2 animate-ping ${
                safetyStatus === 'alert' ? 'border-alert-400/60' : 'border-primary-400/60'
              }`}></div>
              <div className={`absolute inset-2 rounded-full border-2 animate-ping ${
                safetyStatus === 'alert' ? 'border-alert-400/40' : 'border-primary-400/40'
              }`} style={{ animationDelay: '0.5s' }}></div>
              <div className={`absolute inset-4 rounded-full border-2 animate-ping ${
                safetyStatus === 'alert' ? 'border-alert-400/20' : 'border-primary-400/20'
              }`} style={{ animationDelay: '1s' }}></div>
              
              {/* Center Dot */}
              <div className={`absolute inset-0 m-auto w-3 h-3 rounded-full ${
                safetyStatus === 'alert' ? 'bg-alert-400' : 'bg-primary-400'
              } animate-pulse`}></div>
            </div>
          </div>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-gray-500 text-sm">Audio processing inactive</div>
          </div>
        )}
      </div>

      {/* Status Text */}
      <div className="text-center">
        <p className={`text-sm font-medium ${
          isDetecting 
            ? safetyStatus === 'alert' ? 'text-alert-400' : 'text-primary-400'
            : 'text-gray-500'
        }`}>
          {isDetecting 
            ? safetyStatus === 'alert' 
              ? 'THREAT DETECTED - ANALYZING...'
              : 'Listening & Processing...'
            : 'Standby Mode'
          }
        </p>
        
        {isDetecting && (
          <div className="mt-2 flex justify-center gap-4 text-xs text-gray-400">
            <span>Sampling: 44.1kHz</span>
            <span>•</span>
            <span>Buffer: 4s</span>
            <span>•</span>
            <span>CNN Model: Active</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default WaveformDisplay;