import React from 'react';
import { Check, Target, Zap, Code, Server, Lock, Globe, BarChart } from 'lucide-react';

// Brain icon component since it's used but not imported from lucide-react
const Brain = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44A2.5 2.5 0 0 1 2 17.5v-11a2.5 2.5 0 0 1 2.5-2.5h.5" />
    <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44A2.5 2.5 0 0 0 22 17.5v-11a2.5 2.5 0 0 0-2.5-2.5h-.5" />
    <path d="M12 15v.01" />
    <path d="M12 8v.01" />
    <path d="M12 11.5v.01" />
  </svg>
);

const features = [
  {
    icon: <Target className="w-6 h-6 text-blue-400" />,
    title: "97% Detection Accuracy",
    description: "Our advanced algorithms deliver industry-leading accuracy in gunshot detection and classification."
  },
  {
    icon: <Brain className="w-6 h-6 text-purple-400" />,
    title: "CNN Model Training",
    description: "Trained on extensive real-world audio datasets for superior performance in varied environments."
  },
  {
    icon: <Zap className="w-6 h-6 text-yellow-400" />,
    title: "Real-time Response",
    description: "Sub-second alert times ensure the fastest possible emergency response to threats."
  },
  {
    icon: <Code className="w-6 h-6 text-white-400" />,
    title: "Seamless Integration",
    description: "Easily integrates with existing security infrastructure and emergency response systems."
  },
  {
    icon: <Server className="w-6 h-6 text-gray-400" />,
    title: "Scalable Architecture",
    description: "From single buildings to entire cities, our system scales to meet your security needs."
  },
  {
    icon: <Lock className="w-6 h-6 text-green-400" />,
    title: "End-to-End Security",
    description: "Military-grade encryption and secure data handling for maximum protection."
  },
  {
    icon: <Globe className="w-6 h-6 text-primary-400" />,
    title: "Global Deployment",
    description: "Successfully deployed across multiple countries with local emergency service integration."
  },
  {
    icon: <BarChart className="w-6 h-6 text-green-400" />,
    title: "Advanced Analytics",
    description: "Comprehensive reporting and analytics tools to measure and improve security effectiveness."
  }
];

const WhyChoose: React.FC = () => {
  return (
    <section id="why-choose" className="py-16 md:py-24 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-dark-900 to-dark-950 -z-10"></div>
      
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose <span className="text-primary-400">RapidResponse</span></h2>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Our cutting-edge technology offers unmatched reliability and performance in emergency gunshot detection.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="p-6 rounded-xl bg-dark-800/50 backdrop-blur-sm border border-dark-700 hover:border-primary-500/30 transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,174,255,0.15)]"
            >
              <div className="mb-4 flex items-center justify-center">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-2 text-center">{feature.title}</h3>
              <p className="text-gray-400 text-sm text-center">{feature.description}</p>
            </div>
          ))}
        </div>
        
        <div className="mt-16 max-w-4xl mx-auto bg-dark-800/70 backdrop-blur-sm rounded-xl p-6 md:p-8 border border-dark-700">
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="w-full md:w-3/5">
              <h3 className="text-2xl font-bold mb-3">Trusted by Security Professionals</h3>
              <p className="text-gray-300 mb-4">
                RapidResponse has been deployed in over 120 cities worldwide, helping security teams respond faster to critical situations.
              </p>
              <ul className="space-y-2">
                {[
                  'Reduces response time by up to 65%',
                  'Increases successful intervention rates',
                  'Provides accurate location data within 25 meters',
                  'Integrates with MERS 999 and other emergency systems'
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <Check size={20} className="text-primary-400 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-300">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="w-full md:w-2/5 h-48 md:h-auto flex items-center justify-center bg-dark-900 rounded-lg p-4">
              <div className="text-center">
                <div className="text-5xl font-bold text-primary-400 mb-2">95%</div>
                <div className="text-sm text-gray-400">Customer Satisfaction Rate</div>
                <div className="mt-4 flex justify-center">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <svg 
                      key={star} 
                      className="w-5 h-5 text-primary-500 fill-current" 
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChoose;