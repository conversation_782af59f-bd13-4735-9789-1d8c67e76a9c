import React, { useState } from 'react';
import { Play, Circle, Shield, AlertTriangle } from 'lucide-react';
import { useDetection } from '../context/DetectionContext';

const LiveStatusPanel: React.FC = () => {
  const {
    isDetecting,
    setIsDetecting, // ✅ Required for local UI state sync
    safetyStatus,
    runDetection
  } = useDetection();

  const [isProcessing, setIsProcessing] = useState(false);

  const handleStartDetection = async () => {
    if (isProcessing || isDetecting) return;

    setIsProcessing(true);
    setIsDetecting(true); // 🔄 UI shows recording immediately

    try {
      console.log('🎙️ Triggering 6-second backend recording...');
      await runDetection(); // 🔁 Runs /record-and-predict
    } catch (err) {
      console.error('❌ Detection error:', err);
    } finally {
      setIsDetecting(false);
      setIsProcessing(false);
    }
  };

  return (
    <div
      className={`bg-dark-900/80 backdrop-blur-sm rounded-xl p-6 border transition-all duration-500 ${
        safetyStatus === 'alert'
          ? 'border-alert-500/30 shadow-[0_0_30px_rgba(255,61,90,0.15)]'
          : 'border-primary-500/30 shadow-[0_0_30px_rgba(0,174,255,0.15)]'
      }`}
    >
      {/* Detection Control */}
      <div className="mb-6 flex flex-col items-center justify-center text-center">
        <button
          onClick={handleStartDetection}
          disabled={isProcessing}
          className={`w-32 h-32 rounded-full border-4 transition-all duration-300 flex items-center justify-center group mb-4 ${
            isProcessing
              ? 'border-gray-600 bg-gray-600/10'
              : 'border-primary-500 bg-primary-500/10 hover:bg-primary-500/20 shadow-[0_0_40px_rgba(0,174,255,0.3)]'
          }`}
        >
          <Play className="w-12 h-12 text-gray-400 group-hover:text-white group-hover:scale-110 transition-all" />
        </button>

        <div>
          <h3 className="text-xl font-bold mb-1">Start Detection</h3>
          <p className="text-sm text-gray-400">Click to trigger 6s sound check</p>
        </div>
      </div>

      {/* Recording Status */}
      <div className="flex items-center justify-between p-4 rounded-lg bg-dark-800/50 mb-4">
        <div className="flex items-center gap-3">
          <Circle
            className={`w-3 h-3 ${
              isDetecting ? 'text-green-400 animate-pulse' : 'text-gray-500'
            }`}
            fill="currentColor"
          />
          <span className="font-medium">Recording Status</span>
        </div>
        <span
          className={`text-sm font-bold ${
            isDetecting ? 'text-green-400' : 'text-gray-500'
          }`}
        >
          {isDetecting ? 'ON' : 'OFF'}
        </span>
      </div>

      {/* Safety Status */}
      <div
        className={`p-4 rounded-lg border transition-all duration-500 ${
          safetyStatus === 'alert'
            ? 'bg-alert-500/10 border-alert-500/30'
            : 'bg-primary-500/10 border-primary-500/30'
        }`}
      >
        <div className="flex items-center gap-3 mb-2">
          {safetyStatus === 'alert' ? (
            <AlertTriangle className="w-5 h-5 text-alert-400 animate-pulse" />
          ) : (
            <Shield className="w-5 h-5 text-primary-400" />
          )}
          <span className="font-bold">Safety Status</span>
        </div>

        <div
          className={`text-2xl font-bold ${
            safetyStatus === 'alert' ? 'text-alert-400' : 'text-primary-400'
          }`}
        >
          {safetyStatus === 'alert' ? 'GUNSHOT DETECTED' : 'SAFE ZONE'}
        </div>

        {safetyStatus === 'alert' && (
          <p className="text-sm text-alert-300 mt-2">
            Alert active – requires admin clearance
          </p>
        )}
      </div>
    </div>
  );
};

export default LiveStatusPanel;
