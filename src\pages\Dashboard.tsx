import React from 'react';
import { useDetection } from '../context/DetectionContext';
import LiveStatusPanel from '../components/LiveStatusPanel';
import WaveformDisplay from '../components/WaveformDisplay';
import LiveMap from '../components/LiveMap';
import AlertHistory from '../components/AlertHistory';
import Hero from '../components/Hero'; // ⬅️ add Hero import

const Dashboard: React.FC = () => {
  const { safetyStatus } = useDetection();

  return (
    <div className={`min-h-screen transition-all duration-1000 ${
      safetyStatus === 'alert' 
        ? 'bg-gradient-to-br from-alert-950 via-dark-950 to-alert-900' 
        : 'bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900'
    }`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, ${
            safetyStatus === 'alert' ? '#ff3d5a' : '#00aeff'
          } 0%, transparent 50%), radial-gradient(circle at 75% 75%, ${
            safetyStatus === 'alert' ? '#ff3d5a' : '#00aeff'
          } 0%, transparent 50%)`,
          backgroundSize: '100px 100px'
        }}></div>
      </div>

      <div className="relative z-10 pt-24 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-2">
              Live <span className={safetyStatus === 'alert' ? 'text-alert-400' : 'text-primary-400'}>Detection</span> Dashboard
            </h1>
            <p className="text-gray-400">Real-time gunshot detection and monitoring system</p>
          </div>

          {/* Main Dashboard Grid */}
          <div className="grid lg:grid-cols-3 gap-6 mb-8">
            {/* Left Column - Status & Controls */}
            <div className="lg:col-span-1 space-y-6">
              <LiveStatusPanel />
              <WaveformDisplay />
            </div>

            {/* Right Column - Map & Alerts */}
            <div className="lg:col-span-2 space-y-6">
              <LiveMap />
              <AlertHistory />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;