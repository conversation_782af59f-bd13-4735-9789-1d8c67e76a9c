import { initializeApp, getApps, getApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyBdRmm0mPhMOS8CNH6JUlucoezMzELYtls",
  authDomain: "rapidreponseacc.firebaseapp.com",
  projectId: "rapidreponseacc",
  storageBucket: "rapidreponseacc.appspot.com", // ✅ fixed
  messagingSenderId: "913414050278",
  appId: "1:913414050278:web:fdef431503c3574ea90627",
  measurementId: "G-1W5F6BPHKB"
};

// ✅ Check if already initialized
const app = getApps().length ? getApp() : initializeApp(firebaseConfig);

// Auth + Provider
const auth = getAuth(app);
const provider = new GoogleAuthProvider();

export { auth, provider };
