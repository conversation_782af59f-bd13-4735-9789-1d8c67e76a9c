/// <reference types="@types/google.maps" />

let map: google.maps.Map;
let marker: google.maps.Marker;

declare global {
  interface Window {
    initMap: () => void;
  }
}

// Called automatically when Google Maps script loads
window.initMap = () => {
  const defaultPosition = { lat: 37.7749, lng: -122.4194 };

  map = new google.maps.Map(document.getElementById("map") as HTMLElement, {
    zoom: 15,
    center: defaultPosition,
  });

  marker = new google.maps.Marker({
    position: defaultPosition,
    map,
    title: "Microphone Location",
  });

  // Start polling backend
  updateLocation();
  setInterval(updateLocation, 5000);
};

async function updateLocation() {
  try {
    const res = await fetch("/api/location");
    const data = await res.json();

    const latLng = new google.maps.LatLng(data.latitude, data.longitude);

    marker.setPosition(latLng);
    map.setCenter(latLng);
  } catch (err) {
    console.error("Failed to fetch GPS location:", err);
  }
}