import React from 'react';
import { Mail, Phone, MapPin, ExternalLink, Github, Twitter } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-dark-950 text-gray-300 border-t border-dark-800">
      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Logo and about */}
          <div className="md:col-span-1">
            <a href="#" className="flex items-center gap-2 mb-4">
              <img src="/rapidresponse.svg" alt="Rapid Response Logo" className="h-8 w-8 text-blue-500" />
              <span className="text-xl font-bold tracking-tight text-white">
                Rapid<span className="text-primary-400">Response</span>
              </span>
            </a>
            <p className="text-sm text-gray-400 mb-4">
              AI-powered gunshot detection system with real-time emergency response capabilities.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                <Github size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                <ExternalLink size={20} />
              </a>
            </div>
          </div>
          
          {/* Quick links */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              {[
                { label: 'Get Started', href: '#role-selection' },
                { label: 'How It Works', href: '#how-it-works' },
                { label: 'Why Choose Us', href: '#why-choose' },
                { label: 'FAQs', href: '#faqs' },
                { label: 'About Us', href: '/about' },
                { label: 'Contact', href: '/contact' },
              ].map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-gray-400 hover:text-primary-400 transition-colors text-sm"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Contact information */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <Mail size={16} className="text-primary-400 mr-2 mt-0.5" />
                <span className="text-sm"><EMAIL></span>
              </li>
              <li className="flex items-start">
                <Phone size={16} className="text-primary-400 mr-2 mt-0.5" />
                <span className="text-sm">+****************</span>
              </li>
              <li className="flex items-start">
                <MapPin size={16} className="text-primary-400 mr-2 mt-0.5" />
                <span className="text-sm">
                  Taylor's University<br />
                  Campus Lakeside
                </span>
              </li>
            </ul>
          </div>
          
          {/* Newsletter */}
          <div className="md:col-span-1">
            <h3 className="text-lg font-semibold mb-4">Stay Updated</h3>
            <p className="text-sm text-gray-400 mb-3">
              Subscribe to our newsletter for the latest security insights.
            </p>
            <form className="flex">
              <input 
                type="email" 
                placeholder="Your email address" 
                className="flex-grow px-3 py-2 bg-dark-900 border border-dark-700 rounded-l-md focus:outline-none focus:ring-1 focus:ring-primary-500 text-sm"
              />
              <button 
                type="submit" 
                className="bg-primary-500 hover:bg-primary-600 px-3 py-2 rounded-r-md transition-colors"
              >
                <Mail size={16} />
              </button>
            </form>
          </div>
        </div>
        
        <div className="mt-12 pt-6 border-t border-dark-800 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-500 mb-4 md:mb-0">
            &copy; {new Date().getFullYear()} RapidResponse Technologies. All rights reserved.
          </p>
          <div className="flex space-x-4 text-sm text-gray-500">
            <a href="#" className="hover:text-primary-400 transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-primary-400 transition-colors">Terms of Service</a>
            <a href="#" className="hover:text-primary-400 transition-colors">Accessibility</a>
          </div>
        </div>
        
        <div className="mt-6 text-center text-xs text-gray-600">
          <p>Powered by AI. Designed for Safety.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;