import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    question: "How does RapidResponse detect gunshots without an app?",
    answer: "RapidResponse is a browser-based system. You simply enable your microphone permission on our website, and our AI will begin analyzing background sounds in real time—no app download needed."
  },
  {
    question: "Do I need to keep my microphone on all the time?",
    answer: "Yes, for continuous monitoring, your microphone needs to remain enabled while you are on the RapidResponse page. You can turn it off at any time."
  },
  {
    question: "Will the system drain my battery or affect my privacy?",
    answer: "No. The system is optimized for low power consumption. Your audio is not recorded or stored—it is processed in real time using encrypted channels, and only gunshot-relevant signals are analyzed."
  },
  {
    question: "What happens when a gunshot is detected near me?",
    answer: "Once a gunshot is confirmed, the system will immediately send a notification to your device. If you’re within the danger zone, you’ll also receive a recommended evacuation route."
  },
  {
    question: "Is my location shared or saved?",
    answer: "Your location is only used to calculate the safest escape route. We do not store your GPS data permanently."
  },
  {
    question: "Does the system assess the threat level?",
    answer: "Yes. It analyzes the sound’s frequency, intensity, and number of shots to assign a low, moderate, or high threat level."
  },
  {
    question: "Can security personnel confirm or dismiss alerts manually?",
    answer: "Installation is performed by our certified technicians and typically takes 1–2 days depending on facility size. The system includes 24/7 monitoring, automatic updates, and regular maintenance checks."
  },
  {
    question: "Can the system be used indoors and outdoors?",
    answer: "Yes. The system works in both environments. Since it doesn’t rely on physical sensors but uses sound captured by user devices, it is flexible and scalable."
  },
  {
    question: "How fast are alerts sent?",
    answer: "Once a sound is classified as a gunshot, alerts are dispatched within 1–3 seconds depending on network latency."
  },
  {
    question: "Can the system be customized for my organization?",
    answer: "Yes. Schools, malls, government buildings, and city operators can request custom dashboards, response protocols, and integration with internal systems."
  }
];

const FAQ: React.FC = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index)
        ? prev.filter((i) => i !== index)
        : [...prev, index]
    );
  };

  return (
    <section
      id="faqs"
      className="relative py-20 bg-gradient-to-br from-slate-800 to-slate-900 overflow-hidden"
    >
      <div className="absolute inset-0 bg-[linear-gradient(45deg,rgba(59,130,246,0.05)_25%,transparent_25%),linear-gradient(-45deg,rgba(59,130,246,0.05)_25%,transparent_25%),linear-gradient(45deg,transparent_75%,rgba(59,130,246,0.05)_75%),linear-gradient(-45deg,transparent_75%,rgba(59,130,246,0.05)_75%)] bg-[length:60px_60px] bg-[position:0_0,0_30px,30px_-30px,-30px_0] opacity-30 pointer-events-none z-0" />

      <div className="relative z-10 max-w-3xl mx-auto px-4 md:px-6">
        <div className="text-center mb-12">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/10 text-blue-400 text-sm font-medium border border-blue-500/20 mb-4">
            <HelpCircle size={16} className="mr-2 text-blue-400" />
            Frequently Asked Questions
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Everything You Need to{' '}
            <span className="text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">
              Know
            </span>
          </h2>
          <p className="text-lg text-slate-400 max-w-xl mx-auto leading-relaxed">
            Get answers to common questions about our RapidResponse
          </p>
        </div>

        <div className="flex flex-col gap-4">
          {faqData.map((item, index) => {
            const isOpen = openItems.includes(index);
            return (
              <div
                key={index}
                className={`rounded-xl border border-blue-500/10 bg-slate-800/50 backdrop-blur-lg transition-all ${
                  isOpen ? 'shadow-[0_10px_30px_-10px_rgba(59,130,246,0.2)] border-blue-500/30' : ''
                }`}
              >
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full text-left px-6 py-6 flex items-center justify-between hover:bg-blue-500/5 transition-all"
                  aria-expanded={isOpen}
                >
                  <span className="text-white text-lg font-semibold pr-4">
                    {item.question}
                  </span>
                  <span className="text-blue-400">
                    {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                  </span>
                </button>

                <div
                  className={`transition-max-height duration-300 overflow-hidden ${
                    isOpen ? 'max-h-[500px]' : 'max-h-0'
                  }`}
                >
                  <div className="px-6 pb-6 pt-2 border-t border-blue-500/10">
                    <p className="text-slate-400 text-base leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
