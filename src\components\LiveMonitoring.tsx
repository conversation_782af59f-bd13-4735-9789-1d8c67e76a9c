import React, { useState, useEffect } from 'react';
import { AlertTriangle, AlertCircle, Clock, MapPin } from 'lucide-react';

// Mock data for alerts
const mockAlerts = [
  { 
    id: 1, 
    type: 'gunshot', 
    location: 'Downtown District, Block 34', 
    timestamp: '2025-04-02T14:23:05', 
    severity: 'high',
    lat: 40.4,
    lng: -95.15
  },
  { 
    id: 2, 
    type: 'gunshot', 
    location: 'Westside Mall, Parking Area B', 
    timestamp: '2025-04-02T14:10:42', 
    severity: 'medium',
    lat: 40.45,
    lng: -95.2
  },
  { 
    id: 3, 
    type: 'gunshot', 
    location: 'North Industrial Zone', 
    timestamp: '2025-04-02T13:47:19', 
    severity: 'high',
    lat: 40.38,
    lng: -95.18
  }
];

const LiveMonitoring: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [pulsingPoint, setPulsingPoint] = useState(0);
  
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    const pulseTimer = setInterval(() => {
      setPulsingPoint(prev => (prev + 1) % mockAlerts.length);
    }, 3000);
    
    return () => {
      clearInterval(timer);
      clearInterval(pulseTimer);
    };
  }, []);
  
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };
  
  const timeSince = (dateString: string) => {
    const now = new Date();
    const past = new Date(dateString);
    const diffMs = now.getTime() - past.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) {
      return 'just now';
    } else if (diffMins === 1) {
      return '1 minute ago';
    } else {
      return `${diffMins} minutes ago`;
    }
  };

  return (
    <section id="live-monitoring" className="py-16 md:py-24 bg-dark-950">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Live <span className="text-primary-400">Monitoring</span></h2>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Our system provides real-time monitoring and alerts, allowing security teams to respond immediately to potential threats.
          </p>
        </div>
        
        <div className="grid md:grid-cols-5 gap-6 max-w-6xl mx-auto">
          {/* Map display (3 columns) */}
          <div className="md:col-span-3 bg-dark-900 rounded-xl overflow-hidden border border-dark-800 shadow-lg h-[400px] relative">
            {/* Map header */}
            <div className="bg-dark-800 px-4 py-2 flex justify-between items-center">
              <div className="flex items-center">
                <MapPin size={16} className="text-primary-400 mr-2" />
                <span className="text-sm font-medium">Live Monitoring Map</span>
              </div>
              <div className="flex items-center">
                <Clock size={14} className="text-gray-400 mr-1" />
                <span className="text-xs text-gray-400">{formatTime(currentTime)} UTC</span>
              </div>
            </div>
            
            {/* Map content */}
            <div className="relative h-[calc(100%-36px)]">
              {/* Simulated city map grid background */}
              <div className="absolute inset-0 opacity-20" 
                style={{ 
                  backgroundImage: 'linear-gradient(#1e88e5 0.5px, transparent 0.5px), linear-gradient(to right, #1e88e5 0.5px, transparent 0.5px)',
                  backgroundSize: '20px 20px'
                }}
              ></div>
              
              {/* Map markers */}
              {mockAlerts.map((alert, index) => (
                <div 
                  key={alert.id} 
                  className={`absolute w-4 h-4 rounded-full ${
                    alert.severity === 'high' ? 'bg-alert-500' : 'bg-alert-400'
                  } ${index === pulsingPoint ? 'animate-pulse-slow' : ''}`}
                  style={{
                    top: `${(100 - (alert.lat - 40.3) * 500)}px`,
                    left: `${(alert.lng + 96) * 300}px`,
                  }}
                ></div>
              ))}
              
              {/* Overlay text */}
              <div className="absolute bottom-4 left-4 text-xs text-gray-400">
                Simulated map data for demonstration purposes only
              </div>
            </div>
          </div>
          
          {/* Alert panel (2 columns) */}
          <div className="md:col-span-2 bg-dark-900 rounded-xl border border-dark-800 shadow-lg h-[400px] flex flex-col">
            {/* Alert header */}
            <div className="bg-dark-800 px-4 py-2 flex items-center">
              <AlertCircle size={16} className="text-alert-500 mr-2" />
              <span className="text-sm font-medium">Active Alerts</span>
            </div>
            
            {/* Alerts list */}
            <div className="flex-1 overflow-y-auto p-3 space-y-3">
              {mockAlerts.map(alert => (
                <div 
                  key={alert.id} 
                  className={`p-3 rounded-lg ${
                    alert.severity === 'high' 
                      ? 'bg-alert-500/10 border-l-4 border-alert-500' 
                      : 'bg-alert-400/10 border-l-4 border-alert-400'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center">
                      <AlertTriangle size={16} className="text-alert-500 mr-2" />
                      <span className="font-medium">Gunshot Detected</span>
                    </div>
                    <span className="text-xs text-gray-400">{timeSince(alert.timestamp)}</span>
                  </div>
                  <p className="text-sm text-gray-300 mb-1">{alert.location}</p>
                  <div className="flex justify-between items-center">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      alert.severity === 'high' ? 'bg-alert-500/20 text-alert-400' : 'bg-alert-400/20 text-alert-300'
                    }`}>
                      {alert.severity === 'high' ? 'High Priority' : 'Medium Priority'}
                    </span>
                    <button className="text-xs text-primary-400 hover:text-primary-300">View Details</button>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Action buttons */}
            <div className="p-3 border-t border-dark-800 space-x-2">
              <button className="px-3 py-1.5 text-sm rounded-md bg-primary-500 hover:bg-primary-600 transition-colors">
                View All Alerts
              </button>
              <button className="px-3 py-1.5 text-sm rounded-md bg-dark-800 hover:bg-dark-700 transition-colors">
                Contact Emergency Services
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LiveMonitoring;