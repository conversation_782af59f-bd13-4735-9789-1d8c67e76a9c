{"name": "rapidresponse-monitoring", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "date-fns": "^4.1.0", "firebase": "^11.9.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-date-range": "^2.0.1", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-router-dom": "^6.8.1", "wavesurfer.js": "^7.9.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/google.maps": "^3.58.1", "@types/react": "^18.3.5", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.3.0", "@types/wavesurfer.js": "^6.0.12", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}