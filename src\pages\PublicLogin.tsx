import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  sendPasswordResetEmail,
  GoogleAuthProvider
} from 'firebase/auth';
import { auth, provider } from '../firebase';
import {
  Shield,
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowLeft
} from 'lucide-react';

const PublicLogin: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const navigate = useNavigate();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrorMsg('');
    setSuccessMsg('');

    try {
      if (activeTab === 'signup') {
        if (formData.password !== formData.confirmPassword) {
          throw { code: 'password-mismatch' };
        }
        await createUserWithEmailAndPassword(auth, formData.email, formData.password);
        navigate('/dashboard/public');
      } else {
        await signInWithEmailAndPassword(auth, formData.email, formData.password);
        navigate('/dashboard/public');
      }
    } catch (error: any) {
      switch (error.code) {
        case 'auth/user-not-found':
        case 'auth/invalid-credential':
        case 'auth/wrong-password':
          setErrorMsg('Invalid credentials. Please try again.');
          break;
        case 'auth/email-already-in-use':
          setErrorMsg('This email is already in use.');
          break;
        case 'password-mismatch':
          setErrorMsg('Passwords do not match.');
          break;
        default:
          setErrorMsg('Something went wrong. Please try again.');
          break;
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    setErrorMsg('');
    try {
      await signInWithPopup(auth, provider);
      navigate('/dashboard/public');
    } catch (error: any) {
      setErrorMsg('Google login failed.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!formData.email) {
      setErrorMsg('Please enter your email first.');
      return;
    }
    try {
      await sendPasswordResetEmail(auth, formData.email);
      setSuccessMsg('Password reset link sent. Check your inbox.');
    } catch (error: any) {
      setErrorMsg('Failed to send reset email.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900 relative overflow-hidden">
      {/* Background grid and radial styling omitted for brevity */}

      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="max-w-md w-full">
          <button
            onClick={() => navigate('/')}
            className="flex items-center gap-2 text-gray-400 hover:text-white mb-6"
          >
            <ArrowLeft size={20} />
            <span>Back to Role Selection</span>
          </button>

          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-3 rounded-xl bg-green-500/20 border border-green-500/30">
                <Shield className="h-8 w-8 text-green-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">
                  Rapid<span className="text-green-400">Response</span>
                </h1>
                <div className="text-xs text-gray-400">Public Access</div>
              </div>
            </div>
            <h2 className="text-xl font-bold mb-2">Welcome Back</h2>
            <p className="text-gray-400">Access public safety information</p>
          </div>

          <div className="bg-dark-900/80 p-8 rounded-xl border border-green-500/30">
            <div className="flex mb-6 bg-dark-800/50 rounded-lg p-1">
              {['login', 'signup'].map(tab => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as 'login' | 'signup')}
                  className={`flex-1 py-2 rounded-md text-sm font-medium ${
                    activeTab === tab
                      ? 'bg-green-500 text-white shadow-[0_0_10px_rgba(34,197,94,0.3)]'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {tab === 'login' ? 'Login' : 'Sign Up'}
                </button>
              ))}
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {activeTab === 'signup' && (
                <div>
                  <label className="text-sm text-gray-300">Full Name</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg text-white"
                  />
                </div>
              )}

              <div>
                <label className="text-sm text-gray-300">Email Address</label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full pl-12 pr-4 py-3 bg-dark-800 border border-dark-600 rounded-lg text-white"
                  />
                </div>
              </div>

              <div>
                <label className="text-sm text-gray-300">Password</label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className="w-full pl-12 pr-12 py-3 bg-dark-800 border border-dark-600 rounded-lg text-white"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              {activeTab === 'signup' && (
                <div>
                  <label className="text-sm text-gray-300">Confirm Password</label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-dark-800 border border-dark-600 rounded-lg text-white"
                  />
                </div>
              )}

              {errorMsg && (
                <div className="text-sm text-red-400 bg-red-500/10 border border-red-500/30 p-3 rounded-md">
                  {errorMsg}
                </div>
              )}

              {successMsg && (
                <div className="text-sm text-green-400 bg-green-500/10 border border-green-500/30 p-3 rounded-md">
                  {successMsg}
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 rounded-lg"
              >
                {isLoading ? 'Processing...' : activeTab === 'login' ? 'Sign In' : 'Create Account'}
              </button>

              {activeTab === 'login' && (
                <button
                  type="button"
                  onClick={handleForgotPassword}
                  className="mt-2 text-sm text-blue-400 hover:underline"
                >
                  Forgot password?
                </button>
              )}
            </form>

            <div className="mt-6 text-center">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-dark-600" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-dark-900 text-gray-400">Or continue with</span>
                </div>
              </div>

              <button
                onClick={handleGoogleLogin}
                disabled={isLoading}
                className="mt-4 w-full bg-white hover:bg-gray-100 text-gray-900 font-medium py-3 px-6 rounded-lg flex items-center justify-center gap-3"
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                </svg>
                {isLoading ? 'Processing...' : 'Continue with Google'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicLogin;
