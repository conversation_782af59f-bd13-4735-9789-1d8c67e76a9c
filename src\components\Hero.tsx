import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>2, ChevronDown } from 'lucide-react';

const Hero: React.FC = () => {
  return (
    <section className="relative pt-28 pb-16 overflow-hidden md:pt-32 md:pb-20 lg:pt-36 lg:pb-24">
      {/* Radar Detection Animation */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[45vw] h-[45vw] max-w-[45rem] max-h-[45rem] z-0 md:w-[40vw] md:h-[40vw] md:max-w-[40rem] md:max-h-[40rem] sm:w-[50vw] sm:h-[50vw] sm:max-w-[30rem] sm:max-h-[30rem]">
        <div className="relative flex items-center justify-center w-full h-full rounded-full border border-blue-500/20">
          <div className="absolute w-full h-full rounded-full bg-[conic-gradient(rgba(59,130,246,0.3)_0deg,transparent_90deg)] animate-[sweep-rotate_3s_linear_infinite] opacity-20 border border-blue-500/30"></div>
          <div className="absolute w-4 h-4 bg-red-500 rounded-full shadow-[0_0_10px_rgba(239,68,68,0.5)] animate-[ping_1s_cubic-bezier(0,0,0.2,1)_infinite]"></div>
        </div>
      </div>

      {/* Grid Background */}
      <div className="absolute inset-0 opacity-10 bg-[linear-gradient(#1e88e5_1px,transparent_1px),linear-gradient(to_right,#1e88e5_1px,transparent_1px)] bg-[length:40px_40px]"></div>

      {/* Main Content */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 md:px-6 text-center">
        <div className="inline-flex items-center px-3 py-1 rounded-full bg-blue-500/10 text-blue-400 font-medium">
          <AlertTriangle size={16} className="text-red-400 mr-2" />
          React, Resist, Remain Safe
        </div>

        <h1 className="font-bold text-4xl mt-4 mb-6 leading-tight md:text-5xl lg:text-6xl">
          RAPIDRESPONSE,
          <br />
          <span className="text-blue-400">To Protect Your Safety.</span>
        </h1>

        <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto md:text-xl">
          Just turn on your microphone — RapidResponse detects gunshots, assesses threats, and guides you to safety in real time.
        </p>

        {/* Stats */}
        <div className="mt-12 grid grid-cols-2 gap-6 md:grid-cols-4 text-center">
          <div className="p-4 rounded-md bg-gray-800/50 backdrop-blur-md">
            <p className="text-green-400 text-2xl font-bold flex justify-center items-center gap-1">
              <BarChart2 size={22} /> 97%
            </p>
            <p className="text-sm text-gray-400 mt-1">Detection Accuracy</p>
          </div>
          <div className="p-4 rounded-md bg-gray-800/50 backdrop-blur-md">
            <p className="text-green-400 text-2xl font-bold">&lt;1s</p>
            <p className="text-sm text-gray-400 mt-1">Response Time</p>
          </div>
          <div className="p-4 rounded-md bg-gray-800/50 backdrop-blur-md">
            <p className="text-green-400 text-2xl font-bold">24/7</p>
            <p className="text-sm text-gray-400 mt-1">Monitoring</p>
          </div>
          <div className="p-4 rounded-md bg-gray-800/50 backdrop-blur-md">
            <p className="text-green-400 text-2xl font-bold">500m</p>
            <p className="text-sm text-gray-400 mt-1">Detection Range</p>
          </div>
        </div>

        {/* Scroll Arrow */}
        <div className="flex justify-center mt-12">
          <a
            href="#role-selection"
            className="inline-flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full shadow-md hover:bg-blue-700 hover:shadow-lg transform hover:-translate-y-0.5 transition"
          >
            <ChevronDown size={24} className="animate-bounce" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Hero;