import React from 'react';
import { useDetection } from '../context/DetectionContext';
import LiveStatusPanel from '../components/LiveStatusPanel';
import WaveformDisplay from '../components/WaveformDisplay';
import LiveMap from '../components/LiveMap';
import AlertHistory from '../components/AlertHistory';
import { Shield, LogOut } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const AdminDashboard: React.FC = () => {
  const { safetyStatus } = useDetection();
  const navigate = useNavigate();

  const handleLogout = () => {
    navigate('/role-selection');
  };

  return (
    <div
      className={`min-h-screen transition-all duration-1000 ${
        safetyStatus === 'alert'
          ? 'bg-gradient-to-br from-alert-950 via-dark-950 to-alert-900'
          : 'bg-gradient-to-br from-primary-950 via-dark-950 to-primary-900'
      }`}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, ${
              safetyStatus === 'alert' ? '#ff3d5a' : '#00aeff'
            } 0%, transparent 50%), radial-gradient(circle at 75% 75%, ${
              safetyStatus === 'alert' ? '#ff3d5a' : '#00aeff'
            } 0%, transparent 50%)`,
            backgroundSize: '100px 100px',
          }}
        ></div>
      </div>

      {/* Main Body */}
      <div className="relative z-10 pt-8 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          {/* Title */}
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-2">
              Live{' '}
              <span
                className={safetyStatus === 'alert' ? 'text-alert-400' : 'text-purple-400'}
              >
                Detection
              </span>{' '}
              Monitoring
            </h1>
            <p className="text-gray-400">
              Administrator control panel with full system access
            </p>
          </div>

          {/* Main Grid */}
          <div className="grid lg:grid-cols-3 gap-6 mb-8">
            {/* Left Side */}
            <div className="lg:col-span-1 space-y-6">
              <LiveStatusPanel />
              <WaveformDisplay />
            </div>

            {/* Right Side */}
            <div className="lg:col-span-2 space-y-6">
              <LiveMap />
              <AlertHistory /> {/* now supports audio, burst count, remarks, and date range filtering */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
