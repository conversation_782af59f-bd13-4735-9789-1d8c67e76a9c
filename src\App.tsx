import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Navbar from './components/Navbar';
import Dashboard from './pages/Dashboard';
import About from './pages/About';
import Download from './pages/Download';
import Contact from './pages/Contact';
import RoleSelection from './pages/RoleSelection';
import PublicLogin from './pages/PublicLogin';
import AdminLogin from './pages/AdminLogin';
import AdminDashboard from './pages/AdminDashboard';
import PublicDashboard from './pages/PublicDashboard';
import Hero from './components/Hero';
import HowItWorks from './components/HowItWorks';
import WhyChoose from './components/WhyChoose';
import Specific from './components/Specific';
import VideoSection from './components/VideoSection';
import FAQ from './components/FAQ';
import { DetectionProvider } from './context/DetectionContext';
import Footer from './components/Footer';

function App() {
  return (
    <DetectionProvider>
      <Router>
        <div className="min-h-screen bg-dark-950 text-white font-inter">
          <Navbar />
          <Routes>
            {/* Redirect root to role selection */}
            <Route path="/" element={
                <>
                  <Hero />
                  <RoleSelection />
                  <Specific />
                  <HowItWorks />
                  <WhyChoose />
                  <VideoSection />
                  <FAQ />
                </>
              } />

            {/* Authentication Routes */}
            <Route path="/role-selection" element={<RoleSelection />} />
            <Route path="/login/public" element={<PublicLogin />} />
            <Route path="/login/admin" element={<AdminLogin />} />

            {/* Dashboard Routes */}
            <Route path="/dashboard/admin" element={<AdminDashboard />} />
            <Route path="/dashboard/public" element={<PublicDashboard />} />

            {/* Supporting Pages with Navbar */}
            <Route path="/*" element={
              <>
                <Routes>
                  <Route path="/about" element={<About />} />
                  <Route path="/download" element={<Download />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/main" element={<Dashboard />} /> {/* Optional legacy dashboard */}
                </Routes>
              </>
            } />
          </Routes>
          <Footer />
        </div>
      </Router>
    </DetectionProvider>
  );
}

export default App;
