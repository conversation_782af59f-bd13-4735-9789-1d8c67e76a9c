// src/components/WaveSurferPlayer.tsx
import React, { useEffect, useRef } from 'react';
import WaveSurfer from 'wavesurfer.js';

interface WaveSurferPlayerProps {
  url: string;
}

const WaveSurferPlayer: React.FC<WaveSurferPlayerProps> = ({ url }) => {
  const waveformRef = useRef<HTMLDivElement | null>(null);
  const waveSurferRef = useRef<WaveSurfer | null>(null);

  useEffect(() => {
    if (waveformRef.current) {
      waveSurferRef.current = WaveSurfer.create({
        container: waveformRef.current,
        waveColor: '#ccc',
        progressColor: '#00aeff',
        height: 60,
        barWidth: 2,
      });

      waveSurferRef.current.load(url);
    }

    return () => {
      waveSurferRef.current?.destroy();
    };
  }, [url]);

  const handlePlay = () => {
    waveSurferRef.current?.playPause();
  };

  return (
    <div className="mt-2">
      <div ref={waveformRef} className="w-full rounded overflow-hidden" />
      <button
        onClick={handlePlay}
        className="mt-2 px-3 py-1 text-xs bg-dark-800 border border-dark-600 rounded text-white"
      >
        Play / Pause
      </button>
    </div>
  );
};

export default WaveSurferPlayer;
